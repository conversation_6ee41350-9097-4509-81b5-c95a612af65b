# AI Studio 全面系统架构设计方案

## 1. 项目背景与目标

### 1.1 项目背景
AI Studio 是一个基于 Vue3 + TypeScript + Vite + Tauri 2.x 技术栈的中文AI助手桌面应用，专为 Windows/macOS 平台设计。项目旨在提供本地大模型部署、知识库管理、局域网共享等企业级功能，实现完全离线的AI助手解决方案。

### 1.2 项目目标
- **本地化部署**：支持本地大模型推理，无需依赖云端服务
- **知识库管理**：提供文档解析、向量搜索、RAG增强等功能
- **局域网协作**：实现设备间模型、知识库、配置的共享
- **多模态支持**：集成OCR、TTS、语音识别等多媒体处理能力
- **企业级质量**：提供生产环境可用的稳定性和性能
- **插件生态**：支持第三方插件扩展和云端模型API集成

### 1.3 核心功能点
1. **聊天功能**：支持流式对话、多模态输入、会话管理、Markdown渲染
2. **知识库管理**：支持PDF、Word、TXT等格式文档上传，基于ChromaDB实现向量检索和RAG增强
3. **模型管理**：本地模型加载卸载、在线模型下载、性能监控、模型量化
4. **远程配置**：API密钥管理、远程模型配置、代理设置
5. **局域网共享**：mDNS设备发现、P2P文件传输、分布式推理
6. **多模态处理**：图像识别、语音识别(STT)、语音合成(TTS)
7. **系统设置**：主题切换、语言切换、用户信息管理
8. **插件系统**：WASM插件扩展、插件市场、云端API集成

## 2. 完整技术栈

### 2.1 前端技术栈
- **框架**：Vue 3.4+ (Composition API)
- **语言**：TypeScript 5.0+
- **构建工具**：Vite 7.0+
- **状态管理**：Pinia 2.0+
- **UI组件库**：Naive UI 2.0+
- **样式方案**：Tailwind CSS 3.0+ + SCSS
- **路由管理**：Vue Router 4.0+
- **国际化**：Vue I18n 9.0+
- **图标库**：Iconify
- **Markdown渲染**：@vueuse/markdown
- **代码高亮**：Prism.js
- **图表库**：ECharts 5.0+

### 2.2 后端技术栈
- **框架**：Tauri 2.x
- **语言**：Rust 1.75+
- **数据库**：SQLite 3.45+ (主数据库)
- **向量数据库**：ChromaDB (知识库向量存储)
- **AI推理引擎**：Candle-core / llama.cpp
- **网络通信**：Tokio + Reqwest
- **序列化**：Serde + serde_json
- **错误处理**：thiserror + anyhow
- **日志系统**：tracing + tracing-subscriber
- **加密库**：aes-gcm + ring
- **文档解析**：pdf-extract + docx-rs + calamine
- **图像处理**：image + imageproc
- **音频处理**：rodio + whisper-rs
- **网络发现**：mdns + tokio-tungstenite

### 2.3 开发工具链
- **包管理**：npm/pnpm (前端) + Cargo (后端)
- **代码格式化**：Prettier (前端) + rustfmt (后端)
- **代码检查**：ESLint (前端) + Clippy (后端)
- **测试框架**：Vitest (前端) + cargo test (后端)
- **构建工具**：Tauri CLI
- **版本控制**：Git + Git LFS (大模型文件)

## 3. 详细功能模块设计

### 3.1 聊天模块 (Chat)
**功能描述**：提供AI对话、会话管理、流式响应等核心聊天功能

**技术要点**：
- **流式响应**：基于SSE (Server-Sent Events) 实现实时token流输出
- **会话管理**：支持多会话并行、会话历史持久化
- **RAG集成**：自动检索相关知识库内容增强回复质量
- **多模态输入**：支持文本、图片、音频等多种输入方式
- **Markdown渲染**：支持代码高亮、数学公式、表格等
- **消息导出**：支持会话导出为PDF、Word、Markdown格式

**数据库设计**：
```sql
-- 会话表
CREATE TABLE chat_sessions (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    model_id TEXT,
    system_prompt TEXT,
    temperature REAL DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 2048,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 消息表
CREATE TABLE chat_messages (
    id TEXT PRIMARY KEY,
    session_id TEXT REFERENCES chat_sessions(id),
    role TEXT NOT NULL, -- 'user' | 'assistant' | 'system'
    content TEXT NOT NULL,
    attachments JSON, -- 附件信息
    tokens_used INTEGER,
    response_time REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**前后端接口协议**：
- `POST /api/chat/send` - 发送消息
- `GET /api/chat/stream` - SSE流式响应
- `GET /api/chat/sessions` - 获取会话列表
- `POST /api/chat/sessions` - 创建新会话
- `PUT /api/chat/sessions/{id}` - 更新会话配置
- `DELETE /api/chat/sessions/{id}` - 删除会话
- `GET /api/chat/messages/{session_id}` - 获取会话消息
- `POST /api/chat/export/{session_id}` - 导出会话

### 3.2 知识库模块 (Knowledge Base)
**功能描述**：提供文档管理、向量搜索、RAG检索等知识库功能

**技术要点**：
- **文档解析**：支持PDF、Word、Excel、Markdown、TXT等多种格式
- **向量化存储**：使用ChromaDB进行向量存储和检索
- **增量索引**：支持文档变更检测和增量更新
- **语义搜索**：基于embedding模型的高质量语义搜索
- **文档切分**：智能文档分块，保持语义完整性
- **知识图谱**：构建实体关系图，增强检索效果

**数据库设计**：
```sql
-- 知识库表
CREATE TABLE knowledge_bases (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    embedding_model TEXT DEFAULT 'sentence-transformers/all-MiniLM-L6-v2',
    chunk_size INTEGER DEFAULT 512,
    chunk_overlap INTEGER DEFAULT 50,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 文档表
CREATE TABLE documents (
    id TEXT PRIMARY KEY,
    kb_id TEXT REFERENCES knowledge_bases(id),
    name TEXT NOT NULL,
    type TEXT NOT NULL, -- 'pdf', 'docx', 'txt', 'md'
    size INTEGER,
    path TEXT NOT NULL,
    status TEXT DEFAULT 'processing', -- 'processing', 'completed', 'failed'
    chunks_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 文档块表
CREATE TABLE document_chunks (
    id TEXT PRIMARY KEY,
    document_id TEXT REFERENCES documents(id),
    content TEXT NOT NULL,
    metadata JSON,
    page_number INTEGER,
    chunk_index INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**前后端接口协议**：
- `GET /api/knowledge` - 获取知识库列表
- `POST /api/knowledge` - 创建知识库
- `PUT /api/knowledge/{id}` - 更新知识库
- `DELETE /api/knowledge/{id}` - 删除知识库
- `POST /api/knowledge/{id}/upload` - 上传文档
- `GET /api/knowledge/{id}/documents` - 获取文档列表
- `DELETE /api/knowledge/{kb_id}/documents/{doc_id}` - 删除文档
- `POST /api/knowledge/{id}/search` - 语义搜索
- `POST /api/knowledge/{id}/analyze` - 文档分析
- `GET /api/knowledge/{id}/export` - 导出知识库
- `POST /api/knowledge/{id}/import` - 导入知识库

### 3.3 模型管理模块 (Model Management)
**功能描述**：提供本地模型下载、加载、量化、部署等功能

**技术要点**：
- **HuggingFace集成**：支持从HF Hub下载模型，包含镜像站切换
- **断点续传**：支持大文件分片下载和自动恢复
- **模型量化**：集成GPTQ、AWQ等量化工具
- **GPU加速**：支持CUDA、Metal等GPU加速框架
- **一键部署**：自动化模型部署和服务管理
- **性能监控**：实时监控模型推理性能和资源使用

**数据库设计**：
```sql
-- 模型表
CREATE TABLE models (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    display_name TEXT NOT NULL,
    description TEXT,
    author TEXT,
    version TEXT,
    size INTEGER, -- 文件大小(字节)
    quantization TEXT, -- 'none', 'q4_0', 'q4_1', 'q8_0'
    architecture TEXT, -- 'llama', 'mistral', 'qwen'
    context_length INTEGER DEFAULT 2048,
    status TEXT DEFAULT 'available', -- 'available', 'downloading', 'loaded', 'error'
    local_path TEXT,
    download_url TEXT,
    huggingface_id TEXT,
    config JSON, -- 模型配置参数
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 下载任务表
CREATE TABLE download_tasks (
    id TEXT PRIMARY KEY,
    model_id TEXT REFERENCES models(id),
    url TEXT NOT NULL,
    total_size INTEGER,
    downloaded_size INTEGER DEFAULT 0,
    status TEXT DEFAULT 'pending', -- 'pending', 'downloading', 'completed', 'failed', 'paused'
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**前后端接口协议**：
- `GET /api/models` - 获取模型列表
- `GET /api/models/search` - 搜索在线模型
- `POST /api/models/download` - 下载模型
- `POST /api/models/upload` - 上传本地模型
- `POST /api/models/{id}/load` - 加载模型
- `POST /api/models/{id}/unload` - 卸载模型
- `POST /api/models/{id}/quantize` - 模型量化
- `DELETE /api/models/{id}` - 删除模型
- `GET /api/models/{id}/info` - 获取模型详情
- `GET /api/models/downloads` - 获取下载任务列表
- `POST /api/models/downloads/{id}/pause` - 暂停下载
- `POST /api/models/downloads/{id}/resume` - 恢复下载
- `DELETE /api/models/downloads/{id}` - 取消下载

### 3.4 远程配置模块 (Remote Configuration)
**功能描述**：提供API密钥管理、远程模型配置、代理设置等功能

**技术要点**：
- **API密钥管理**：支持OpenAI、Anthropic、Google等主流AI服务商
- **代理配置**：支持HTTP/HTTPS/SOCKS5代理设置
- **配置同步**：支持配置云端备份和多设备同步
- **安全存储**：敏感信息加密存储，支持主密码保护
- **配置模板**：预设常用配置模板，快速配置
- **配置验证**：自动验证API密钥有效性和网络连通性

**数据库设计**：
```sql
-- 远程配置表
CREATE TABLE remote_configs (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    provider TEXT NOT NULL, -- 'openai', 'anthropic', 'google', 'custom'
    api_key TEXT NOT NULL, -- 加密存储
    base_url TEXT,
    model_name TEXT,
    max_tokens INTEGER DEFAULT 2048,
    temperature REAL DEFAULT 0.7,
    proxy_type TEXT, -- 'none', 'http', 'https', 'socks5'
    proxy_host TEXT,
    proxy_port INTEGER,
    proxy_username TEXT,
    proxy_password TEXT, -- 加密存储
    is_active BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 配置模板表
CREATE TABLE config_templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    provider TEXT NOT NULL,
    config JSON NOT NULL,
    is_builtin BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**前后端接口协议**：
- `GET /api/remote/configs` - 获取配置列表
- `POST /api/remote/configs` - 创建配置
- `PUT /api/remote/configs/{id}` - 更新配置
- `DELETE /api/remote/configs/{id}` - 删除配置
- `POST /api/remote/configs/{id}/test` - 测试配置连通性
- `POST /api/remote/configs/{id}/activate` - 激活配置
- `GET /api/remote/templates` - 获取配置模板
- `POST /api/remote/templates` - 创建自定义模板
- `POST /api/remote/sync/backup` - 备份配置到云端
- `POST /api/remote/sync/restore` - 从云端恢复配置
